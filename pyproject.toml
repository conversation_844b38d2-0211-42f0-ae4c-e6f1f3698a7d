[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "captioner-translate"
version = "0.1.0"
description = "A subtitle translation tool using OpenAI API"
readme = "README.md"
requires-python = ">=3.9"
authors = [
    {name = "Captioner Translate", email = "<EMAIL>"},
]
license = {text = "MIT"}
keywords = ["subtitle", "translation", "openai", "cli"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Video",
    "Topic :: Text Processing :: Linguistic",
]
dependencies = [
    "openai>=1.82.0",
    "python-dotenv>=1.1.0",
    "requests>=2.32.3",
    "retry>=0.9.2",
    "typer>=0.12.0",
    "rich>=13.0.0",
    "httpx[socks]>=0.28.1",
]

[project.scripts]
translate = "captioner_translate.cli:app"

[project.urls]
Homepage = "https://github.com/example/captioner-translate"
Repository = "https://github.com/example/captioner-translate"
Issues = "https://github.com/example/captioner-translate/issues"
